use crate::dto::dwd::{file_detail::FileDetail, sub_test_item_detail::SubTestItemDetail};
use crate::dwd::util::dwd_common_util::DwdCommonUtil;
use crate::utils::date::IntoDateTimeUtc;
use crate::utils::decimal::{Decimal38_18, IntoDecimal38_18};
use chrono::{DateTime, Utc};
use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::borrow::Cow;
use std::sync::Arc;

/// Optimized version of TestItemDetailRow that uses Arc and Cow to minimize clones
/// This struct uses shared references where possible to reduce memory allocation overhead
#[derive(Row, Serialize, Deserialize, Debug, PartialEq, Clone)]
#[allow(non_snake_case)]
pub struct TestItemDetailRowOptimized {
    pub ID: String,
    // Use Arc for frequently shared file detail strings
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FILE_ID: u32,
    pub FILE_NAME: Arc<str>,
    pub FILE_TYPE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub WAFER_LOT_ID: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub OFFLINE_RETEST: Option<u8>,
    pub ONLINE_RETEST: Option<u8>,
    pub INTERRUPT: Option<u8>,
    pub DUP_RETEST: Option<u8>,
    pub BATCH_NUM: Option<u8>,
    pub OFFLINE_RETEST_IGNORE_TP: Option<u8>,
    pub INTERRUPT_IGNORE_TP: Option<u8>,
    pub DUP_RETEST_IGNORE_TP: Option<u8>,
    pub BATCH_NUM_IGNORE_TP: Option<u8>,
    pub MAX_OFFLINE_RETEST: Option<u8>,
    pub MAX_ONLINE_RETEST: Option<u8>,
    pub IS_DIE_FIRST_TEST: Option<u8>,
    pub IS_DIE_FINAL_TEST: Option<u8>,
    pub IS_FIRST_TEST: Option<u8>,
    pub IS_FINAL_TEST: Option<u8>,
    pub IS_FIRST_TEST_IGNORE_TP: Option<u8>,
    pub IS_FINAL_TEST_IGNORE_TP: Option<u8>,
    pub IS_DUP_FIRST_TEST: Option<u8>,
    pub IS_DUP_FINAL_TEST: Option<u8>,
    pub IS_DUP_FIRST_TEST_IGNORE_TP: Option<u8>,
    pub IS_DUP_FINAL_TEST_IGNORE_TP: Option<u8>,
    pub TEST_SUITE: Arc<str>,
    // Use Arc for HashMap to avoid deep clones
    pub CONDITION_SET: Arc<Vec<(String, String)>>,
    pub TEST_NUM: Option<u32>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub IS_DIE_FIRST_TEST_ITEM: Option<u8>,
    pub TESTITEM_TYPE: Arc<str>,
    pub TEST_FLG: Arc<str>,
    pub PARM_FLG: Arc<str>,
    pub TEST_STATE: Arc<str>,
    pub TEST_VALUE: Option<Decimal38_18>,
    pub UNITS: Arc<str>,
    pub TEST_RESULT: Option<u8>,
    pub ORIGIN_TEST_VALUE: Option<Decimal38_18>,
    pub ORIGIN_UNITS: Arc<str>,
    pub TEST_ORDER: Option<u32>,
    pub ALARM_ID: Arc<str>,
    pub OPT_FLG: Arc<str>,
    pub RES_SCAL: Option<i32>,
    pub NUM_TEST: Option<i32>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub LLM_SCAL: Option<i32>,
    pub HLM_SCAL: Option<i32>,
    pub LO_LIMIT: Option<Decimal38_18>,
    pub HI_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_HI_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_LO_LIMIT: Option<Decimal38_18>,
    pub C_RESFMT: Arc<str>,
    pub C_LLMFMT: Arc<str>,
    pub C_HLMFMT: Arc<str>,
    pub LO_SPEC: Option<Decimal38_18>,
    pub HI_SPEC: Option<Decimal38_18>,
    pub SPEC_NAM: Arc<str>,
    pub SPEC_VER: Arc<str>,
    pub HBIN_NUM: Option<u32>,
    pub SBIN_NUM: Option<u32>,
    pub SBIN_PF: Arc<str>,
    pub SBIN_NAM: Arc<str>,
    pub HBIN_PF: Arc<str>,
    pub HBIN_NAM: Arc<str>,
    pub HBIN: Arc<str>,
    pub SBIN: Arc<str>,
    pub TEST_HEAD: Option<u32>,
    pub TESTER_NAME: Arc<str>,
    pub TESTER_TYPE: Arc<str>,
    pub OPERATOR_NAME: Arc<str>,
    pub PROBER_HANDLER_TYP: Arc<str>,
    pub PROBER_HANDLER_ID: Arc<str>,
    pub PROBECARD_LOADBOARD_TYP: Arc<str>,
    pub PROBECARD_LOADBOARD_ID: Arc<str>,
    pub PART_FLG: Arc<str>,
    pub PART_ID: Arc<str>,
    pub C_PART_ID: Option<u32>,
    pub ECID: Arc<str>,
    pub ECID_EXT: Arc<str>,
    pub ECID_EXTRA: Arc<Vec<(String, String)>>,
    pub IS_STANDARD_ECID: Option<u8>,
    pub X_COORD: Option<i32>,
    pub Y_COORD: Option<i32>,
    pub DIE_X: Option<i32>,
    pub DIE_Y: Option<i32>,
    pub TEST_TIME: Option<u32>,
    pub PART_TXT: Arc<str>,
    pub PART_FIX: Arc<str>,
    pub TOUCH_DOWN_ID: Option<u32>,
    pub SITE: Option<u32>,
    pub SITE_GRP: Option<u32>,
    pub SITE_CNT: Option<u32>,
    pub SITE_NUMS: Arc<str>,
    pub TEXT_DAT: String,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub START_TIME: Option<DateTime<Utc>>,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub END_TIME: Option<DateTime<Utc>>,
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,
    pub WAFER_ID: Arc<str>,
    pub WAFER_NO: Arc<str>,
    pub WAFER_SIZE: Option<Decimal38_18>,
    pub WAFER_MARGIN: Option<Decimal38_18>,
    pub DIE_HEIGHT: Option<Decimal38_18>,
    pub DIE_WIDTH: Option<Decimal38_18>,
    pub WF_UNITS: Option<u32>,
    pub WF_FLAT: Arc<str>,
    pub CENTER_X: Option<i32>,
    pub CENTER_Y: Option<i32>,
    pub CENTER_OFFSET_X: Option<Decimal38_18>,
    pub CENTER_OFFSET_Y: Option<Decimal38_18>,
    pub CENTER_RETICLE_X: Option<i32>,
    pub CENTER_RETICLE_Y: Option<u8>,
    pub CENTER_RETICLE_OFFSET_X: Option<Decimal38_18>,
    pub CENTER_RETICLE_OFFSET_Y: Option<Decimal38_18>,
    pub POS_X: Arc<str>,
    pub POS_Y: Arc<str>,
    pub DIE_CNT: Option<u32>,
    pub RETICLE_T_X: Option<i32>,
    pub RETICLE_T_Y: Option<i32>,
    pub RETICLE_X: Option<i32>,
    pub RETICLE_Y: Option<i32>,
    pub RETICLE_ROW: Option<u32>,
    pub RETICLE_COLUMN: Option<u32>,
    pub RETICLE_ROW_CENTER_OFFSET: Option<i32>,
    pub RETICLE_COLUMN_CENTER_OFFSET: Option<i32>,
    pub ORIGINAL_WAFER_SIZE: Option<Decimal38_18>,
    pub ORIGINAL_WAFER_MARGIN: Option<Decimal38_18>,
    pub ORIGINAL_WF_UNITS: Option<u32>,
    pub ORIGINAL_WF_FLAT: Arc<str>,
    pub ORIGINAL_POS_X: Arc<str>,
    pub ORIGINAL_POS_Y: Arc<str>,
    pub ORIGINAL_DIE_WIDTH: Option<Decimal38_18>,
    pub ORIGINAL_DIE_HEIGHT: Option<Decimal38_18>,
    pub ORIGINAL_RETICLE_ROW: Option<u32>,
    pub ORIGINAL_RETICLE_COLUMN: Option<u32>,
    pub ORIGINAL_RETICLE_ROW_CENTER_OFFSET: Option<i32>,
    pub ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET: Option<i32>,
    pub ORIGINAL_CENTER_X: Option<i32>,
    pub ORIGINAL_CENTER_Y: Option<i32>,
    pub ORIGINAL_CENTER_RETICLE_X: Option<i32>,
    pub ORIGINAL_CENTER_RETICLE_Y: Option<u8>,
    pub ORIGINAL_CENTER_OFFSET_X: Option<Decimal38_18>,
    pub ORIGINAL_CENTER_OFFSET_Y: Option<Decimal38_18>,
    pub ORIGINAL_CENTER_RETICLE_OFFSET_X: Option<Decimal38_18>,
    pub ORIGINAL_CENTER_RETICLE_OFFSET_Y: Option<Decimal38_18>,
    pub SITE_ID: Arc<str>,
    pub PART_CNT: Option<u32>,
    pub RTST_CNT: Option<u32>,
    pub ABRT_CNT: Option<u32>,
    pub GOOD_CNT: Option<u32>,
    pub FUNC_CNT: Option<u32>,
    pub FABWF_ID: Arc<str>,
    pub FRAME_ID: Arc<str>,
    pub MASK_ID: Arc<str>,
    pub WAFER_USR_DESC: Arc<str>,
    pub WAFER_EXC_DESC: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub SETUP_T: Option<DateTime<Utc>>,
    pub STAT_NUM: Option<u32>,
    pub MODE_COD: Arc<str>,
    pub PROT_COD: Arc<str>,
    pub BURN_TIM: Option<u32>,
    pub CMOD_COD: Arc<str>,
    pub EXEC_TYP: Arc<str>,
    pub EXEC_VER: Arc<str>,
    pub USER_TXT: Arc<str>,
    pub AUX_FILE: Arc<str>,
    pub PKG_TYP: Arc<str>,
    pub FAMLY_ID: Arc<str>,
    pub DATE_COD: Arc<str>,
    pub FACIL_ID: Arc<str>,
    pub FLOOR_ID: Arc<str>,
    pub PROC_ID: Arc<str>,
    pub OPER_FRQ: Arc<str>,
    pub FLOW_ID: Arc<str>,
    pub FLOW_ID_IGNORE_TP: Arc<str>,
    pub SETUP_ID: Arc<str>,
    pub DSGN_REV: Arc<str>,
    pub ENG_ID: Arc<str>,
    pub ROM_COD: Arc<str>,
    pub SERL_NUM: Arc<str>,
    pub SUPR_NAM: Arc<str>,
    pub DISP_COD: Arc<str>,
    pub LOT_USR_DESC: Arc<str>,
    pub LOT_EXC_DESC: Arc<str>,
    pub DIB_TYP: Arc<str>,
    pub DIB_ID: Arc<str>,
    pub CABL_TYP: Arc<str>,
    pub CABL_ID: Arc<str>,
    pub CONT_TYP: Arc<str>,
    pub CONT_ID: Arc<str>,
    pub LASR_TYP: Arc<str>,
    pub LASR_ID: Arc<str>,
    pub EXTR_TYP: Arc<str>,
    pub EXTR_ID: Arc<str>,
    pub RETEST_BIN_NUM: Arc<str>,
    pub VECT_NAM: Arc<str>,
    pub TIME_SET: Arc<str>,
    pub NUM_FAIL: Option<u32>,
    pub FAIL_PIN: Arc<str>,
    pub CYCL_CNT: Option<u32>,
    pub REPT_CNT: Option<u32>,
    pub LONG_ATTRIBUTE_SET: Arc<Vec<(String, i64)>>,
    pub STRING_ATTRIBUTE_SET: Arc<Vec<(String, String)>>,
    pub FLOAT_ATTRIBUTE_SET: Arc<Vec<(String, Decimal38_18)>>,
    pub UID: Arc<str>,
    pub CREATE_HOUR_KEY: Arc<str>,
    pub CREATE_DAY_KEY: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub CREATE_TIME: DateTime<Utc>,
    pub CREATE_USER: Arc<str>,
    pub LOT_BUCKET: i32,
    pub IS_DELETE: u8,
    pub PROCESS: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub UPLOAD_TIME: DateTime<Utc>,
    pub DATA_VERSION: i64,
    pub EFUSE_EXTRA: Arc<Vec<(String, String)>>,
    pub CHIP_ID: Arc<str>,
}

impl TestItemDetailRowOptimized {
    /// Create optimized TestItemDetailRow with minimal clones using Arc for shared data
    pub fn new_optimized(sub_test_item_detail: &SubTestItemDetail, file_detail: &FileDetail) -> Self {
        Self {
            ID: String::new(),
            CUSTOMER: Arc::from(file_detail.CUSTOMER.as_str()),
            SUB_CUSTOMER: Arc::from(file_detail.SUB_CUSTOMER.as_str()),
            UPLOAD_TYPE: Arc::from(file_detail.UPLOAD_TYPE.as_str()),
            FILE_ID: sub_test_item_detail.FILE_ID.unwrap() as u32,
            FILE_NAME: Arc::from(file_detail.FILE_NAME.as_str()),
            FILE_TYPE: Arc::from(file_detail.FILE_TYPE.as_str()),
            DEVICE_ID: Arc::from(file_detail.DEVICE_ID.as_str()),
            FACTORY: Arc::from(file_detail.FACTORY.as_str()),
            FACTORY_SITE: Arc::from(file_detail.FACTORY_SITE.as_str()),
            FAB: Arc::from(file_detail.FAB.as_str()),
            FAB_SITE: Arc::from(file_detail.FAB_SITE.as_str()),
            LOT_TYPE: Arc::from(file_detail.LOT_TYPE.as_str()),
            LOT_ID: Arc::from(file_detail.LOT_ID.as_str()),
            SBLOT_ID: Arc::from(file_detail.SBLOT_ID.as_str()),
            WAFER_LOT_ID: Arc::from(sub_test_item_detail.WAFER_LOT_ID.as_ref().unwrap().as_str()),
            TEST_AREA: Arc::from(file_detail.TEST_AREA.as_str()),
            TEST_STAGE: Arc::from(file_detail.TEST_STAGE.as_str()),
            OFFLINE_RETEST: file_detail.OFFLINE_RETEST,
            OFFLINE_RETEST_IGNORE_TP: file_detail.OFFLINE_RETEST_IGNORE_TP,
            ONLINE_RETEST: sub_test_item_detail.ONLINE_RETEST.map(|v| v as u8),
            INTERRUPT: file_detail.INTERRUPT,
            INTERRUPT_IGNORE_TP: file_detail.INTERRUPT_IGNORE_TP,
            DUP_RETEST: file_detail.DUP_RETEST,
            DUP_RETEST_IGNORE_TP: file_detail.DUP_RETEST_IGNORE_TP,
            BATCH_NUM: file_detail.BATCH_NUM,
            BATCH_NUM_IGNORE_TP: file_detail.BATCH_NUM_IGNORE_TP,
            MAX_OFFLINE_RETEST: sub_test_item_detail.MAX_OFFLINE_RETEST.map(|v| v as u8),
            MAX_ONLINE_RETEST: sub_test_item_detail.MAX_ONLINE_RETEST.map(|v| v as u8),
            IS_DIE_FIRST_TEST: sub_test_item_detail.IS_DIE_FIRST_TEST.map(|v| v as u8),
            IS_DIE_FINAL_TEST: sub_test_item_detail.IS_DIE_FINAL_TEST.map(|v| v as u8),
            IS_FIRST_TEST: sub_test_item_detail.IS_FIRST_TEST.map(|v| v as u8),
            IS_FINAL_TEST: sub_test_item_detail.IS_FINAL_TEST.map(|v| v as u8),
            IS_FIRST_TEST_IGNORE_TP: sub_test_item_detail.IS_FIRST_TEST_IGNORE_TP.map(|v| v as u8),
            IS_FINAL_TEST_IGNORE_TP: sub_test_item_detail.IS_FINAL_TEST_IGNORE_TP.map(|v| v as u8),
            IS_DUP_FIRST_TEST: sub_test_item_detail.IS_DUP_FIRST_TEST.map(|v| v as u8),
            IS_DUP_FINAL_TEST: sub_test_item_detail.IS_DUP_FINAL_TEST.map(|v| v as u8),
            IS_DUP_FIRST_TEST_IGNORE_TP: sub_test_item_detail.IS_DUP_FIRST_TEST_IGNORE_TP.map(|v| v as u8),
            IS_DUP_FINAL_TEST_IGNORE_TP: sub_test_item_detail.IS_DUP_FINAL_TEST_IGNORE_TP.map(|v| v as u8),
            TEST_SUITE: Arc::from(sub_test_item_detail.TEST_SUITE.as_ref().unwrap().as_str()),
            CONDITION_SET: Arc::new(
                sub_test_item_detail
                    .CONDITION_SET
                    .as_ref()
                    .map(|map| {
                        map.iter()
                            .map(|(k, v)| (k.clone(), v.clone()))
                            .collect::<Vec<(String, String)>>()
                    })
                    .unwrap_or_default(),
            ),
            TEST_NUM: sub_test_item_detail.TEST_NUM.map(|v| v as u32),
            TEST_TXT: Arc::from(sub_test_item_detail.TEST_TXT.as_ref().unwrap().as_str()),
            TEST_ITEM: Arc::from(sub_test_item_detail.TEST_ITEM.as_ref().unwrap().as_str()),
            IS_DIE_FIRST_TEST_ITEM: sub_test_item_detail.IS_DIE_FIRST_TEST_ITEM.map(|v| v as u8),
            TESTITEM_TYPE: Arc::from(sub_test_item_detail.TESTITEM_TYPE.as_ref().unwrap().as_str()),
            TEST_FLG: Arc::from(sub_test_item_detail.TEST_FLG.as_ref().unwrap().as_str()),
            PARM_FLG: Arc::from(sub_test_item_detail.PARM_FLG.as_ref().unwrap().as_str()),
            TEST_STATE: Arc::from(sub_test_item_detail.TEST_STATE.as_ref().unwrap().as_str()),
            TEST_VALUE: sub_test_item_detail.TEST_VALUE.map(|v| v.into_decimal38_18()),
            UNITS: Arc::from(sub_test_item_detail.UNITS.as_ref().unwrap().as_str()),
            TEST_RESULT: sub_test_item_detail.TEST_RESULT.map(|v| v as u8),
            ORIGIN_TEST_VALUE: sub_test_item_detail.ORIGIN_TEST_VALUE.map(|v| v.into_decimal38_18()),
            ORIGIN_UNITS: Arc::from(sub_test_item_detail.ORIGIN_UNITS.as_ref().unwrap().as_str()),
            TEST_ORDER: sub_test_item_detail.TEST_ORDER.map(|v| v as u32),
            ALARM_ID: Arc::from(sub_test_item_detail.ALARM_ID.as_ref().unwrap().as_str()),
            OPT_FLG: Arc::from(sub_test_item_detail.OPT_FLG.as_ref().unwrap().as_str()),
            RES_SCAL: sub_test_item_detail.RES_SCAL,
            NUM_TEST: sub_test_item_detail.NUM_TEST,
            TEST_PROGRAM: Arc::from(file_detail.TEST_PROGRAM.as_str()),
            TEST_TEMPERATURE: Arc::from(file_detail.TEST_TEMPERATURE.as_str()),
            TEST_PROGRAM_VERSION: Arc::from(file_detail.TEST_PROGRAM_VERSION.as_str()),
            LLM_SCAL: sub_test_item_detail.LLM_SCAL,
            HLM_SCAL: sub_test_item_detail.HLM_SCAL,
            LO_LIMIT: sub_test_item_detail.LO_LIMIT.map(|v| v.into_decimal38_18()),
            HI_LIMIT: sub_test_item_detail.HI_LIMIT.map(|v| v.into_decimal38_18()),
            ORIGIN_HI_LIMIT: sub_test_item_detail.ORIGIN_HI_LIMIT.map(|v| v.into_decimal38_18()),
            ORIGIN_LO_LIMIT: sub_test_item_detail.ORIGIN_LO_LIMIT.map(|v| v.into_decimal38_18()),
            C_RESFMT: Arc::from(sub_test_item_detail.C_RESFMT.as_ref().unwrap().as_str()),
            C_LLMFMT: Arc::from(sub_test_item_detail.C_LLMFMT.as_ref().unwrap().as_str()),
            C_HLMFMT: Arc::from(sub_test_item_detail.C_HLMFMT.as_ref().unwrap().as_str()),
            LO_SPEC: sub_test_item_detail.LO_SPEC.map(|v| v.into_decimal38_18()),
            HI_SPEC: sub_test_item_detail.HI_SPEC.map(|v| v.into_decimal38_18()),
            SPEC_NAM: Arc::from(file_detail.SPEC_NAM.as_str()),
            SPEC_VER: Arc::from(file_detail.SPEC_VER.as_str()),
            HBIN_NUM: sub_test_item_detail.HBIN_NUM.map(|v| v as u32),
            SBIN_NUM: sub_test_item_detail.SBIN_NUM.map(|v| v as u32),
            SBIN_PF: Arc::from(sub_test_item_detail.SBIN_PF.as_ref().unwrap().as_str()),
            SBIN_NAM: Arc::from(sub_test_item_detail.SBIN_NAM.as_ref().unwrap().as_str()),
            HBIN_PF: Arc::from(sub_test_item_detail.HBIN_PF.as_ref().unwrap().as_str()),
            HBIN_NAM: Arc::from(sub_test_item_detail.HBIN_NAM.as_ref().unwrap().as_str()),
            HBIN: Arc::from(sub_test_item_detail.HBIN.as_ref().unwrap().as_str()),
            SBIN: Arc::from(sub_test_item_detail.SBIN.as_ref().unwrap().as_str()),
            TEST_HEAD: sub_test_item_detail.TEST_HEAD.map(|v| v as u32),
            TESTER_NAME: Arc::from(file_detail.TESTER_NAME.as_str()),
            TESTER_TYPE: Arc::from(file_detail.TESTER_TYPE.as_str()),
            OPERATOR_NAME: Arc::from(file_detail.OPERATOR_NAME.as_str()),
            PROBER_HANDLER_TYP: Arc::from(file_detail.PROBER_HANDLER_TYP.as_str()),
            PROBER_HANDLER_ID: Arc::from(file_detail.PROBER_HANDLER_ID.as_str()),
            PROBECARD_LOADBOARD_TYP: Arc::from(file_detail.PROBECARD_LOADBOARD_TYP.as_str()),
            PROBECARD_LOADBOARD_ID: Arc::from(file_detail.PROBECARD_LOADBOARD_ID.as_str()),
            PART_FLG: Arc::from(sub_test_item_detail.PART_FLG.as_ref().unwrap().as_str()),
            PART_ID: Arc::from(sub_test_item_detail.PART_ID.as_ref().unwrap().as_str()),
            C_PART_ID: sub_test_item_detail.C_PART_ID.map(|v| v as u32),
            ECID: Arc::from(sub_test_item_detail.ECID.as_ref().unwrap().as_str()),
            ECID_EXT: Arc::from(sub_test_item_detail.ECID_EXT.as_ref().unwrap().as_str()),
            ECID_EXTRA: Arc::new(
                sub_test_item_detail
                    .ECID_EXTRA
                    .as_ref()
                    .map(|map| {
                        map.iter()
                            .map(|(k, v)| (k.clone(), v.clone()))
                            .collect::<Vec<(String, String)>>()
                    })
                    .unwrap_or_default(),
            ),
            IS_STANDARD_ECID: sub_test_item_detail.IS_STANDARD_ECID.map(|v| v as u8),
            X_COORD: sub_test_item_detail.X_COORD,
            Y_COORD: sub_test_item_detail.Y_COORD,
            DIE_X: sub_test_item_detail.DIE_X,
            DIE_Y: sub_test_item_detail.DIE_Y,
            TEST_TIME: sub_test_item_detail.TEST_TIME.map(|v| v as u32),
            PART_TXT: Arc::from(sub_test_item_detail.PART_TXT.as_ref().unwrap().as_str()),
            PART_FIX: Arc::from(sub_test_item_detail.PART_FIX.as_ref().unwrap().as_str()),
            SITE: sub_test_item_detail.SITE.map(|v| v as u32),
            SITE_GRP: file_detail.SITE_GRP,
            SITE_CNT: file_detail.SITE_CNT,
            TOUCH_DOWN_ID: sub_test_item_detail.TOUCH_DOWN_ID.map(|v| v as u32),
            SITE_NUMS: Arc::from(file_detail.SITE_NUMS.as_str()),
            START_TIME: file_detail.START_TIME,
            END_TIME: file_detail.END_TIME,
            START_HOUR_KEY: Arc::from(file_detail.START_HOUR_KEY.as_str()),
            START_DAY_KEY: Arc::from(file_detail.START_DAY_KEY.as_str()),
            END_HOUR_KEY: Arc::from(file_detail.END_HOUR_KEY.as_str()),
            END_DAY_KEY: Arc::from(file_detail.END_DAY_KEY.as_str()),
            WAFER_ID: Arc::from(sub_test_item_detail.WAFER_ID.as_ref().unwrap().as_str()),
            WAFER_NO: Arc::from(sub_test_item_detail.WAFER_NO.as_ref().unwrap().as_str()),
            WAFER_SIZE: file_detail.WAFER_SIZE,
            WAFER_MARGIN: file_detail.WAFER_MARGIN,
            DIE_HEIGHT: file_detail.DIE_HEIGHT,
            DIE_WIDTH: file_detail.DIE_WIDTH,
            WF_UNITS: file_detail.WF_UNITS,
            WF_FLAT: Arc::from(file_detail.WF_FLAT.as_str()),
            CENTER_X: file_detail.CENTER_X,
            CENTER_Y: file_detail.CENTER_Y,
            CENTER_OFFSET_X: file_detail.CENTER_OFFSET_X,
            CENTER_OFFSET_Y: file_detail.CENTER_OFFSET_Y,
            CENTER_RETICLE_X: file_detail.CENTER_RETICLE_X,
            CENTER_RETICLE_Y: file_detail.CENTER_RETICLE_Y.map(|v| v as u8),
            CENTER_RETICLE_OFFSET_X: file_detail.CENTER_RETICLE_OFFSET_X,
            CENTER_RETICLE_OFFSET_Y: file_detail.CENTER_RETICLE_OFFSET_Y,
            POS_X: Arc::from(file_detail.POS_X.as_str()),
            POS_Y: Arc::from(file_detail.POS_Y.as_str()),
            DIE_CNT: file_detail.DIE_CNT,
            RETICLE_T_X: sub_test_item_detail.RETICLE_T_X,
            RETICLE_T_Y: sub_test_item_detail.RETICLE_T_Y,
            RETICLE_X: sub_test_item_detail.RETICLE_X,
            RETICLE_Y: sub_test_item_detail.RETICLE_Y,
            RETICLE_ROW: file_detail.RETICLE_ROW,
            RETICLE_COLUMN: file_detail.RETICLE_COLUMN,
            RETICLE_ROW_CENTER_OFFSET: file_detail.RETICLE_ROW_CENTER_OFFSET,
            RETICLE_COLUMN_CENTER_OFFSET: file_detail.RETICLE_COLUMN_CENTER_OFFSET,
            ORIGINAL_WAFER_SIZE: file_detail.ORIGINAL_WAFER_SIZE,
            ORIGINAL_WAFER_MARGIN: file_detail.ORIGINAL_WAFER_MARGIN,
            ORIGINAL_WF_UNITS: file_detail.ORIGINAL_WF_UNITS,
            ORIGINAL_WF_FLAT: Arc::from(file_detail.ORIGINAL_WF_FLAT.as_str()),
            ORIGINAL_POS_X: Arc::from(file_detail.ORIGINAL_POS_X.as_str()),
            ORIGINAL_POS_Y: Arc::from(file_detail.ORIGINAL_POS_Y.as_str()),
            ORIGINAL_DIE_WIDTH: file_detail.ORIGINAL_DIE_WIDTH,
            ORIGINAL_DIE_HEIGHT: file_detail.ORIGINAL_DIE_HEIGHT,
            ORIGINAL_RETICLE_ROW: file_detail.ORIGINAL_RETICLE_ROW,
            ORIGINAL_RETICLE_COLUMN: file_detail.ORIGINAL_RETICLE_COLUMN,
            ORIGINAL_RETICLE_ROW_CENTER_OFFSET: file_detail.ORIGINAL_RETICLE_ROW_CENTER_OFFSET,
            ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET: file_detail.ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET,
            ORIGINAL_CENTER_X: file_detail.ORIGINAL_CENTER_X,
            ORIGINAL_CENTER_Y: file_detail.ORIGINAL_CENTER_Y,
            ORIGINAL_CENTER_RETICLE_X: file_detail.ORIGINAL_CENTER_RETICLE_X,
            ORIGINAL_CENTER_RETICLE_Y: file_detail.ORIGINAL_CENTER_RETICLE_Y.map(|v| v as u8),
            ORIGINAL_CENTER_OFFSET_X: file_detail.ORIGINAL_CENTER_OFFSET_X,
            ORIGINAL_CENTER_OFFSET_Y: file_detail.ORIGINAL_CENTER_OFFSET_Y,
            ORIGINAL_CENTER_RETICLE_OFFSET_X: file_detail.ORIGINAL_CENTER_RETICLE_OFFSET_X,
            ORIGINAL_CENTER_RETICLE_OFFSET_Y: file_detail.ORIGINAL_CENTER_RETICLE_OFFSET_Y,
            SITE_ID: Arc::from(sub_test_item_detail.SITE_ID.as_ref().unwrap().as_str()),
            PART_CNT: file_detail.PART_CNT,
            RTST_CNT: file_detail.RTST_CNT,
            ABRT_CNT: file_detail.ABRT_CNT,
            GOOD_CNT: file_detail.GOOD_CNT,
            FUNC_CNT: file_detail.FUNC_CNT,
            FABWF_ID: Arc::from(file_detail.FABWF_ID.as_str()),
            FRAME_ID: Arc::from(file_detail.FRAME_ID.as_str()),
            MASK_ID: Arc::from(file_detail.MASK_ID.as_str()),
            WAFER_USR_DESC: Arc::from(file_detail.WAFER_USR_DESC.as_str()),
            WAFER_EXC_DESC: Arc::from(file_detail.WAFER_EXC_DESC.as_str()),
            SETUP_T: file_detail.SETUP_T,
            STAT_NUM: file_detail.STAT_NUM,
            MODE_COD: Arc::from(file_detail.MODE_COD.as_str()),
            PROT_COD: Arc::from(file_detail.PROT_COD.as_str()),
            BURN_TIM: file_detail.BURN_TIM,
            CMOD_COD: Arc::from(file_detail.CMOD_COD.as_str()),
            EXEC_TYP: Arc::from(file_detail.EXEC_TYP.as_str()),
            EXEC_VER: Arc::from(file_detail.EXEC_VER.as_str()),
            USER_TXT: Arc::from(file_detail.USER_TXT.as_str()),
            AUX_FILE: Arc::from(file_detail.AUX_FILE.as_str()),
            PKG_TYP: Arc::from(file_detail.PKG_TYP.as_str()),
            FAMLY_ID: Arc::from(file_detail.FAMLY_ID.as_str()),
            DATE_COD: Arc::from(file_detail.DATE_COD.as_str()),
            FACIL_ID: Arc::from(file_detail.FACIL_ID.as_str()),
            FLOOR_ID: Arc::from(file_detail.FLOOR_ID.as_str()),
            PROC_ID: Arc::from(file_detail.PROC_ID.as_str()),
            OPER_FRQ: Arc::from(file_detail.OPER_FRQ.as_str()),
            FLOW_ID: Arc::from(file_detail.FLOW_ID.as_str()),
            FLOW_ID_IGNORE_TP: Arc::from(file_detail.FLOW_ID_IGNORE_TP.as_str()),
            SETUP_ID: Arc::from(file_detail.SETUP_ID.as_str()),
            DSGN_REV: Arc::from(file_detail.DSGN_REV.as_str()),
            ENG_ID: Arc::from(file_detail.ENG_ID.as_str()),
            ROM_COD: Arc::from(file_detail.ROM_COD.as_str()),
            SERL_NUM: Arc::from(file_detail.SERL_NUM.as_str()),
            SUPR_NAM: Arc::from(file_detail.SUPR_NAM.as_str()),
            DISP_COD: Arc::from(file_detail.DISP_COD.as_str()),
            LOT_USR_DESC: Arc::from(file_detail.LOT_USR_DESC.as_str()),
            LOT_EXC_DESC: Arc::from(file_detail.LOT_EXC_DESC.as_str()),
            DIB_TYP: Arc::from(file_detail.DIB_TYP.as_str()),
            DIB_ID: Arc::from(file_detail.DIB_ID.as_str()),
            CABL_TYP: Arc::from(file_detail.CABL_TYP.as_str()),
            CABL_ID: Arc::from(file_detail.CABL_ID.as_str()),
            CONT_TYP: Arc::from(file_detail.CONT_TYP.as_str()),
            CONT_ID: Arc::from(file_detail.CONT_ID.as_str()),
            LASR_TYP: Arc::from(file_detail.LASR_TYP.as_str()),
            LASR_ID: Arc::from(file_detail.LASR_ID.as_str()),
            EXTR_TYP: Arc::from(file_detail.EXTR_TYP.as_str()),
            EXTR_ID: Arc::from(file_detail.EXTR_ID.as_str()),
            RETEST_BIN_NUM: Arc::from(file_detail.RETEST_BIN_NUM.as_str()),
            VECT_NAM: Arc::from(sub_test_item_detail.VECT_NAM.as_ref().unwrap().as_str()),
            TIME_SET: Arc::from(sub_test_item_detail.TIME_SET.as_ref().unwrap().as_str()),
            NUM_FAIL: sub_test_item_detail.NUM_FAIL.map(|i| i as u32),
            FAIL_PIN: Arc::from(sub_test_item_detail.FAIL_PIN.as_ref().unwrap().as_str()),
            CYCL_CNT: sub_test_item_detail.CYCL_CNT.map(|i| i as u32),
            REPT_CNT: sub_test_item_detail.REPT_CNT.map(|i| i as u32),
            LONG_ATTRIBUTE_SET: Arc::from(
                sub_test_item_detail
                    .LONG_ATTRIBUTE_SET
                    .as_ref()
                    .map(|map| map.iter().map(|(k, v)| (k.clone(), *v)).collect::<Vec<(String, i64)>>())
                    .unwrap_or_default(),
            ),
            STRING_ATTRIBUTE_SET: Arc::from(
                sub_test_item_detail
                    .STRING_ATTRIBUTE_SET
                    .as_ref()
                    .map(|map| {
                        map.iter()
                            .map(|(k, v)| (k.clone(), v.clone()))
                            .collect::<Vec<(String, String)>>()
                    })
                    .unwrap_or_default(),
            ),
            FLOAT_ATTRIBUTE_SET: Arc::from(
                sub_test_item_detail
                    .FLOAT_ATTRIBUTE_SET
                    .as_ref()
                    .map(|map| {
                        map.iter()
                            .map(|(k, v)| (k.clone(), v.into_decimal38_18()))
                            .collect::<Vec<(String, Decimal38_18)>>()
                    })
                    .unwrap_or_default(),
            ),
            UID: Arc::from(sub_test_item_detail.UID.as_ref().unwrap().as_str()),
            TEXT_DAT: DwdCommonUtil::string_value(sub_test_item_detail.TEXT_DAT.clone()),
            CREATE_HOUR_KEY: Arc::from(sub_test_item_detail.CREATE_HOUR_KEY.as_ref().unwrap().as_str()),
            CREATE_DAY_KEY: Arc::from(sub_test_item_detail.CREATE_DAY_KEY.as_ref().unwrap().as_str()),
            CREATE_TIME: sub_test_item_detail.CREATE_TIME.into_utc(),
            CREATE_USER: Arc::from(file_detail.CREATE_USER.as_str()),
            LOT_BUCKET: file_detail.LOT_BUCKET,
            IS_DELETE: file_detail.IS_DELETE,
            PROCESS: Arc::from(DwdCommonUtil::str_value(file_detail.PROCESS.as_ref().map(|s| s.as_str()))),
            UPLOAD_TIME: DwdCommonUtil::cal_upload_time(
                file_detail.UPLOAD_TIME,
                sub_test_item_detail.CREATE_TIME.into_utc(),
            ),
            DATA_VERSION: DwdCommonUtil::cal_data_version(file_detail.DATA_VERSION),
            EFUSE_EXTRA: Arc::new(
                sub_test_item_detail
                    .EFUSE_EXTRA
                    .as_ref()
                    .map(|map| {
                        map.iter()
                            .map(|(k, v)| (k.clone(), v.clone()))
                            .collect::<Vec<(String, String)>>()
                    })
                    .unwrap_or_default(),
            ),
            CHIP_ID: Arc::from(sub_test_item_detail.CHIP_ID.as_ref().unwrap().as_str()),
        }
    }
}
