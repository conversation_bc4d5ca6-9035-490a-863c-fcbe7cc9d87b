use crate::dwd::util::dwd_common_util::DwdCommonUtil;
use crate::utils::decimal::{Decimal38_18, IntoDecimal38_18};
use chrono::{DateTime, Utc};
use clickhouse::Row;
use serde::{Deserialize, Serialize};




#[derive(Row, Serialize, Deserialize, Debug, PartialEq)]
#[allow(non_snake_case)]
pub struct TestItemDetailRow {
    pub ID: String,
    pub CUSTOMER: String,
    pub SUB_CUSTOMER: String,
    pub UPLOAD_TYPE: String,
    pub FILE_ID: u32,
    pub FILE_NAME: String,
    pub FILE_TYPE: String,
    pub DEVICE_ID: String,
    pub FACTORY: String,
    pub FACTORY_SITE: String,
    pub FAB: String,
    pub FAB_SITE: String,
    pub LOT_TYPE: String,
    pub LOT_ID: String,
    pub PROCESS: String,
    pub SBLOT_ID: String,
    pub WAFER_LOT_ID: String,
    pub TEST_AREA: String,
    pub TEST_STAGE: String,
    pub OFFLINE_RETEST: Option<u8>,
    pub ONLINE_RETEST: Option<u8>,
    pub INTERRUPT: Option<u8>,
    pub DUP_RETEST: Option<u8>,
    pub BATCH_NUM: Option<u8>,
    pub OFFLINE_RETEST_IGNORE_TP: Option<u8>,
    pub INTERRUPT_IGNORE_TP: Option<u8>,
    pub DUP_RETEST_IGNORE_TP: Option<u8>,
    pub BATCH_NUM_IGNORE_TP: Option<u8>,
    pub MAX_OFFLINE_RETEST: Option<u8>,
    pub MAX_ONLINE_RETEST: Option<u8>,
    pub IS_DIE_FIRST_TEST: Option<u8>,
    pub IS_DIE_FINAL_TEST: Option<u8>,
    pub IS_FIRST_TEST: Option<u8>,
    pub IS_FINAL_TEST: Option<u8>,
    pub IS_FIRST_TEST_IGNORE_TP: Option<u8>,
    pub IS_FINAL_TEST_IGNORE_TP: Option<u8>,
    pub IS_DUP_FIRST_TEST: Option<u8>,
    pub IS_DUP_FINAL_TEST: Option<u8>,
    pub IS_DUP_FIRST_TEST_IGNORE_TP: Option<u8>,
    pub IS_DUP_FINAL_TEST_IGNORE_TP: Option<u8>,
    pub TEST_SUITE: String,
    pub CONDITION_SET: Vec<(String, String)>,
    pub TEST_NUM: Option<u32>,
    pub TEST_TXT: String,
    pub TEST_ITEM: String,
    pub IS_DIE_FIRST_TEST_ITEM: Option<u8>,
    pub TESTITEM_TYPE: String,
    pub TEST_FLG: String,
    pub PARM_FLG: String,
    pub TEST_STATE: String,
    pub TEST_VALUE: Option<Decimal38_18>,
    pub UNITS: String,
    pub TEST_RESULT: Option<u8>,
    pub ORIGIN_TEST_VALUE: Option<Decimal38_18>,
    pub ORIGIN_UNITS: String,
    pub TEST_ORDER: Option<u32>,
    pub ALARM_ID: String,
    pub OPT_FLG: String,
    pub RES_SCAL: Option<i32>,
    pub NUM_TEST: Option<i32>,
    pub TEST_PROGRAM: String,
    pub TEST_TEMPERATURE: String,
    pub TEST_PROGRAM_VERSION: String,
    pub LLM_SCAL: Option<i32>,
    pub HLM_SCAL: Option<i32>,
    pub LO_LIMIT: Option<Decimal38_18>,
    pub HI_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_HI_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_LO_LIMIT: Option<Decimal38_18>,
    pub C_RESFMT: String,
    pub C_LLMFMT: String,
    pub C_HLMFMT: String,
    pub LO_SPEC: Option<Decimal38_18>,
    pub HI_SPEC: Option<Decimal38_18>,
    pub SPEC_NAM: String,
    pub SPEC_VER: String,
    pub HBIN_NUM: Option<u32>,
    pub SBIN_NUM: Option<u32>,
    pub SBIN_PF: String,
    pub SBIN_NAM: String,
    pub HBIN_PF: String,
    pub HBIN_NAM: String,
    pub HBIN: String,
    pub SBIN: String,
    pub TEST_HEAD: Option<u32>,
    pub TESTER_NAME: String,
    pub TESTER_TYPE: String,
    pub OPERATOR_NAME: String,
    pub PROBER_HANDLER_TYP: String,
    pub PROBER_HANDLER_ID: String,
    pub PROBECARD_LOADBOARD_TYP: String,
    pub PROBECARD_LOADBOARD_ID: String,
    pub PART_FLG: String,
    pub PART_ID: String,
    pub C_PART_ID: Option<u32>,
    pub UID: String,
    pub ECID: String,
    pub ECID_EXT: String,
    pub ECID_EXTRA: Vec<(String, String)>,
    pub IS_STANDARD_ECID: Option<u8>,
    pub X_COORD: Option<i32>,
    pub Y_COORD: Option<i32>,
    pub DIE_X: Option<i32>,
    pub DIE_Y: Option<i32>,
    pub TEST_TIME: Option<u32>,
    pub PART_TXT: String,
    pub PART_FIX: String,
    pub TOUCH_DOWN_ID: Option<u32>,
    pub SITE: Option<u32>,
    pub SITE_GRP: Option<u32>,
    pub SITE_CNT: Option<u32>,
    pub SITE_NUMS: String,
    pub TEXT_DAT: String,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub START_TIME: Option<DateTime<Utc>>,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub END_TIME: Option<DateTime<Utc>>,
    pub START_HOUR_KEY: String,
    pub START_DAY_KEY: String,
    pub END_HOUR_KEY: String,
    pub END_DAY_KEY: String,
    pub WAFER_ID: String,
    pub WAFER_NO: String,
    pub WAFER_SIZE: Option<Decimal38_18>,
    pub WAFER_MARGIN: Option<Decimal38_18>,
    pub DIE_HEIGHT: Option<Decimal38_18>,
    pub DIE_WIDTH: Option<Decimal38_18>,
    pub WF_UNITS: Option<u32>,
    pub WF_FLAT: String,
    pub CENTER_X: Option<i32>,
    pub CENTER_Y: Option<i32>,
    pub CENTER_OFFSET_X: Option<Decimal38_18>,
    pub CENTER_OFFSET_Y: Option<Decimal38_18>,
    pub CENTER_RETICLE_X: Option<i32>,
    pub CENTER_RETICLE_Y: Option<u8>,
    pub CENTER_RETICLE_OFFSET_X: Option<Decimal38_18>,
    pub CENTER_RETICLE_OFFSET_Y: Option<Decimal38_18>,
    pub POS_X: String,
    pub POS_Y: String,
    pub DIE_CNT: Option<u32>,
    pub RETICLE_T_X: Option<i32>,
    pub RETICLE_T_Y: Option<i32>,
    pub RETICLE_X: Option<i32>,
    pub RETICLE_Y: Option<i32>,
    pub RETICLE_ROW: Option<u32>,
    pub RETICLE_COLUMN: Option<u32>,
    pub RETICLE_ROW_CENTER_OFFSET: Option<i32>,
    pub RETICLE_COLUMN_CENTER_OFFSET: Option<i32>,
    pub ORIGINAL_WAFER_SIZE: Option<Decimal38_18>,
    pub ORIGINAL_WAFER_MARGIN: Option<Decimal38_18>,
    pub ORIGINAL_WF_UNITS: Option<u32>,
    pub ORIGINAL_WF_FLAT: String,
    pub ORIGINAL_POS_X: String,
    pub ORIGINAL_POS_Y: String,
    pub ORIGINAL_DIE_WIDTH: Option<Decimal38_18>,
    pub ORIGINAL_DIE_HEIGHT: Option<Decimal38_18>,
    pub ORIGINAL_RETICLE_ROW: Option<u32>,
    pub ORIGINAL_RETICLE_COLUMN: Option<u32>,
    pub ORIGINAL_RETICLE_ROW_CENTER_OFFSET: Option<i32>,
    pub ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET: Option<i32>,
    pub ORIGINAL_CENTER_X: Option<i32>,
    pub ORIGINAL_CENTER_Y: Option<i32>,
    pub ORIGINAL_CENTER_RETICLE_X: Option<i32>,
    pub ORIGINAL_CENTER_RETICLE_Y: Option<u8>,
    pub ORIGINAL_CENTER_OFFSET_X: Option<Decimal38_18>,
    pub ORIGINAL_CENTER_OFFSET_Y: Option<Decimal38_18>,
    pub ORIGINAL_CENTER_RETICLE_OFFSET_X: Option<Decimal38_18>,
    pub ORIGINAL_CENTER_RETICLE_OFFSET_Y: Option<Decimal38_18>,
    pub SITE_ID: String,
    pub PART_CNT: Option<u32>,
    pub RTST_CNT: Option<u32>,
    pub ABRT_CNT: Option<u32>,
    pub GOOD_CNT: Option<u32>,
    pub FUNC_CNT: Option<u32>,
    pub FABWF_ID: String,
    pub FRAME_ID: String,
    pub MASK_ID: String,
    pub WAFER_USR_DESC: String,
    pub WAFER_EXC_DESC: String,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub SETUP_T: Option<DateTime<Utc>>,
    pub STAT_NUM: Option<u32>,
    pub MODE_COD: String,
    pub PROT_COD: String,
    pub BURN_TIM: Option<u32>,
    pub CMOD_COD: String,
    pub EXEC_TYP: String,
    pub EXEC_VER: String,
    pub USER_TXT: String,
    pub AUX_FILE: String,
    pub PKG_TYP: String,
    pub FAMLY_ID: String,
    pub DATE_COD: String,
    pub FACIL_ID: String,
    pub FLOOR_ID: String,
    pub PROC_ID: String,
    pub OPER_FRQ: String,
    pub FLOW_ID: String,
    pub FLOW_ID_IGNORE_TP: String,
    pub SETUP_ID: String,
    pub DSGN_REV: String,
    pub ENG_ID: String,
    pub ROM_COD: String,
    pub SERL_NUM: String,
    pub SUPR_NAM: String,
    pub DISP_COD: String,
    pub LOT_USR_DESC: String,
    pub LOT_EXC_DESC: String,
    pub DIB_TYP: String,
    pub DIB_ID: String,
    pub CABL_TYP: String,
    pub CABL_ID: String,
    pub CONT_TYP: String,
    pub CONT_ID: String,
    pub LASR_TYP: String,
    pub LASR_ID: String,
    pub EXTR_TYP: String,
    pub EXTR_ID: String,
    pub EFUSE_EXTRA: Vec<(String, String)>,
    pub CHIP_ID: String,
    pub RETEST_BIN_NUM: String,
    pub VECT_NAM: String,
    pub TIME_SET: String,
    pub NUM_FAIL: Option<u32>,
    pub FAIL_PIN: String,
    pub CYCL_CNT: Option<u32>,
    pub REPT_CNT: Option<u32>,
    pub LONG_ATTRIBUTE_SET: Vec<(String, i64)>,
    pub STRING_ATTRIBUTE_SET: Vec<(String, String)>,
    pub FLOAT_ATTRIBUTE_SET: Vec<(String, Decimal38_18)>,
    pub CREATE_HOUR_KEY: String,
    pub CREATE_DAY_KEY: String,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub CREATE_TIME: DateTime<Utc>,
    pub CREATE_USER: String,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub UPLOAD_TIME: DateTime<Utc>,
    pub DATA_VERSION: i64,
    pub LOT_BUCKET: i32,
    pub IS_DELETE: u8,
}

use crate::dto::dwd::{file_detail::FileDetail, sub_test_item_detail::SubTestItemDetail};
use crate::utils::date::IntoDateTimeUtc;

impl TestItemDetailRow {
    pub fn new(sub_test_item_detail: &SubTestItemDetail, file_detail: &FileDetail) -> Self {
        Self {
            ID: String::new(),
            CUSTOMER: file_detail.CUSTOMER.clone(),
            SUB_CUSTOMER: file_detail.SUB_CUSTOMER.clone(),
            UPLOAD_TYPE: file_detail.UPLOAD_TYPE.clone(),
            FILE_ID: sub_test_item_detail.FILE_ID.unwrap() as u32,
            FILE_NAME: file_detail.FILE_NAME.clone(),
            FILE_TYPE: file_detail.FILE_TYPE.clone(),
            DEVICE_ID: file_detail.DEVICE_ID.clone(),
            FACTORY: file_detail.FACTORY.clone(),
            FACTORY_SITE: file_detail.FACTORY_SITE.clone(),
            FAB: file_detail.FAB.clone(),
            FAB_SITE: file_detail.FAB_SITE.clone(),
            LOT_TYPE: file_detail.LOT_TYPE.clone(),
            LOT_ID: file_detail.LOT_ID.clone(),
            SBLOT_ID: file_detail.SBLOT_ID.clone(),
            WAFER_LOT_ID: sub_test_item_detail.WAFER_LOT_ID.clone().unwrap(),
            TEST_AREA: file_detail.TEST_AREA.clone(),
            TEST_STAGE: file_detail.TEST_STAGE.clone(),
            OFFLINE_RETEST: file_detail.OFFLINE_RETEST,
            OFFLINE_RETEST_IGNORE_TP: file_detail.OFFLINE_RETEST_IGNORE_TP,
            ONLINE_RETEST: sub_test_item_detail.ONLINE_RETEST.map(|v| v as u8).clone(),
            INTERRUPT: file_detail.INTERRUPT,
            INTERRUPT_IGNORE_TP: file_detail.INTERRUPT_IGNORE_TP,
            DUP_RETEST: file_detail.DUP_RETEST,
            DUP_RETEST_IGNORE_TP: file_detail.DUP_RETEST_IGNORE_TP,
            BATCH_NUM: file_detail.BATCH_NUM,
            BATCH_NUM_IGNORE_TP: file_detail.BATCH_NUM_IGNORE_TP,
            MAX_OFFLINE_RETEST: sub_test_item_detail.MAX_OFFLINE_RETEST.map(|v| v as u8),
            MAX_ONLINE_RETEST: sub_test_item_detail.MAX_ONLINE_RETEST.map(|v| v as u8),
            IS_DIE_FIRST_TEST: sub_test_item_detail.IS_DIE_FIRST_TEST.map(|v| v as u8),
            IS_DIE_FINAL_TEST: sub_test_item_detail.IS_DIE_FINAL_TEST.map(|v| v as u8),
            IS_FIRST_TEST: sub_test_item_detail.IS_FIRST_TEST.map(|v| v as u8),
            IS_FINAL_TEST: sub_test_item_detail.IS_FINAL_TEST.map(|v| v as u8),
            IS_FIRST_TEST_IGNORE_TP: sub_test_item_detail.IS_FIRST_TEST_IGNORE_TP.map(|v| v as u8),
            IS_FINAL_TEST_IGNORE_TP: sub_test_item_detail.IS_FINAL_TEST_IGNORE_TP.map(|v| v as u8),
            IS_DUP_FIRST_TEST: sub_test_item_detail.IS_DUP_FIRST_TEST.map(|v| v as u8),
            IS_DUP_FINAL_TEST: sub_test_item_detail.IS_DUP_FINAL_TEST.map(|v| v as u8),
            IS_DUP_FIRST_TEST_IGNORE_TP: sub_test_item_detail.IS_DUP_FIRST_TEST_IGNORE_TP.map(|v| v as u8),
            IS_DUP_FINAL_TEST_IGNORE_TP: sub_test_item_detail.IS_DUP_FINAL_TEST_IGNORE_TP.map(|v| v as u8),
            TEST_SUITE: sub_test_item_detail.TEST_SUITE.clone().unwrap(),
            CONDITION_SET: sub_test_item_detail
                .CONDITION_SET
                .as_ref()
                .map(|map| {
                    map.iter()
                        .map(|(k, v)| (k.clone(), v.clone()))
                        .collect::<Vec<(String, String)>>()
                })
                .unwrap_or_default(),
            TEST_NUM: sub_test_item_detail.TEST_NUM.map(|v| v as u32),
            TEST_TXT: sub_test_item_detail.TEST_TXT.clone().unwrap(),
            TEST_ITEM: sub_test_item_detail.TEST_ITEM.clone().unwrap(),
            IS_DIE_FIRST_TEST_ITEM: sub_test_item_detail.IS_DIE_FIRST_TEST_ITEM.map(|v| v as u8),
            TESTITEM_TYPE: sub_test_item_detail.TESTITEM_TYPE.clone().unwrap(),
            TEST_FLG: sub_test_item_detail.TEST_FLG.clone().unwrap(),
            PARM_FLG: sub_test_item_detail.PARM_FLG.clone().unwrap(),
            TEST_STATE: sub_test_item_detail.TEST_STATE.clone().unwrap(),
            TEST_VALUE: sub_test_item_detail.TEST_VALUE.map(|v| v.into_decimal38_18()),
            UNITS: sub_test_item_detail.UNITS.clone().unwrap(),
            TEST_RESULT: sub_test_item_detail.TEST_RESULT.map(|v| v as u8),
            ORIGIN_TEST_VALUE: sub_test_item_detail.ORIGIN_TEST_VALUE.map(|v| v.into_decimal38_18()),
            ORIGIN_UNITS: sub_test_item_detail.ORIGIN_UNITS.clone().unwrap(),
            TEST_ORDER: sub_test_item_detail.TEST_ORDER.map(|v| v as u32),
            ALARM_ID: sub_test_item_detail.ALARM_ID.clone().unwrap(),
            OPT_FLG: sub_test_item_detail.OPT_FLG.clone().unwrap(),
            RES_SCAL: sub_test_item_detail.RES_SCAL,
            NUM_TEST: sub_test_item_detail.NUM_TEST,
            TEST_PROGRAM: file_detail.TEST_PROGRAM.clone(),
            TEST_TEMPERATURE: file_detail.TEST_TEMPERATURE.clone(),
            TEST_PROGRAM_VERSION: file_detail.TEST_PROGRAM_VERSION.clone(),
            LLM_SCAL: sub_test_item_detail.LLM_SCAL,
            HLM_SCAL: sub_test_item_detail.HLM_SCAL,
            LO_LIMIT: sub_test_item_detail.LO_LIMIT.map(|v| v.into_decimal38_18()),
            HI_LIMIT: sub_test_item_detail.HI_LIMIT.map(|v| v.into_decimal38_18()),
            ORIGIN_HI_LIMIT: sub_test_item_detail.ORIGIN_HI_LIMIT.map(|v| v.into_decimal38_18()),
            ORIGIN_LO_LIMIT: sub_test_item_detail.ORIGIN_LO_LIMIT.map(|v| v.into_decimal38_18()),
            C_RESFMT: sub_test_item_detail.C_RESFMT.clone().unwrap(),
            C_LLMFMT: sub_test_item_detail.C_LLMFMT.clone().unwrap(),
            C_HLMFMT: sub_test_item_detail.C_HLMFMT.clone().unwrap(),
            LO_SPEC: sub_test_item_detail.LO_SPEC.map(|v| v.into_decimal38_18()),
            HI_SPEC: sub_test_item_detail.HI_SPEC.map(|v| v.into_decimal38_18()),
            SPEC_NAM: file_detail.SPEC_NAM.clone(),
            SPEC_VER: file_detail.SPEC_VER.clone(),
            HBIN_NUM: sub_test_item_detail.HBIN_NUM.map(|v| v as u32),
            SBIN_NUM: sub_test_item_detail.SBIN_NUM.map(|v| v as u32),
            SBIN_PF: sub_test_item_detail.SBIN_PF.clone().unwrap(),
            SBIN_NAM: sub_test_item_detail.SBIN_NAM.clone().unwrap(),
            HBIN_PF: sub_test_item_detail.HBIN_PF.clone().unwrap(),
            HBIN_NAM: sub_test_item_detail.HBIN_NAM.clone().unwrap(),
            HBIN: sub_test_item_detail.HBIN.clone().unwrap(),
            SBIN: sub_test_item_detail.SBIN.clone().unwrap(),
            TEST_HEAD: sub_test_item_detail.TEST_HEAD.map(|v| v as u32),
            TESTER_NAME: file_detail.TESTER_NAME.clone(),
            TESTER_TYPE: file_detail.TESTER_TYPE.clone(),
            OPERATOR_NAME: file_detail.OPERATOR_NAME.clone(),
            PROBER_HANDLER_TYP: file_detail.PROBER_HANDLER_TYP.clone(),
            PROBER_HANDLER_ID: file_detail.PROBER_HANDLER_ID.clone(),
            PROBECARD_LOADBOARD_TYP: file_detail.PROBECARD_LOADBOARD_TYP.clone(),
            PROBECARD_LOADBOARD_ID: file_detail.PROBECARD_LOADBOARD_ID.clone(),
            PART_FLG: sub_test_item_detail.PART_FLG.clone().unwrap(),
            PART_ID: sub_test_item_detail.PART_ID.clone().unwrap(),
            C_PART_ID: sub_test_item_detail.C_PART_ID.map(|v| v as u32),
            ECID: sub_test_item_detail.ECID.clone().unwrap(),
            ECID_EXT: sub_test_item_detail.ECID_EXT.clone().unwrap(),
            ECID_EXTRA: sub_test_item_detail
                .ECID_EXTRA
                .as_ref()
                .map(|map| {
                    map.iter()
                        .map(|(k, v)| (k.clone(), v.clone()))
                        .collect::<Vec<(String, String)>>()
                })
                .unwrap_or_default(),
            IS_STANDARD_ECID: sub_test_item_detail.IS_STANDARD_ECID.map(|v| v as u8),
            X_COORD: sub_test_item_detail.X_COORD,
            Y_COORD: sub_test_item_detail.Y_COORD,
            DIE_X: sub_test_item_detail.DIE_X,
            DIE_Y: sub_test_item_detail.DIE_Y,
            TEST_TIME: sub_test_item_detail.TEST_TIME.map(|v| v as u32),
            PART_TXT: sub_test_item_detail.PART_TXT.clone().unwrap(),
            PART_FIX: sub_test_item_detail.PART_FIX.clone().unwrap(),
            SITE: sub_test_item_detail.SITE.map(|v| v as u32),
            SITE_GRP: file_detail.SITE_GRP,
            SITE_CNT: file_detail.SITE_CNT,
            TOUCH_DOWN_ID: sub_test_item_detail.TOUCH_DOWN_ID.map(|v| v as u32),
            SITE_NUMS: file_detail.SITE_NUMS.clone(),
            START_TIME: file_detail.START_TIME,
            END_TIME: file_detail.END_TIME,
            START_HOUR_KEY: file_detail.START_HOUR_KEY.clone(),
            START_DAY_KEY: file_detail.START_DAY_KEY.clone(),
            END_HOUR_KEY: file_detail.END_HOUR_KEY.clone(),
            END_DAY_KEY: file_detail.END_DAY_KEY.clone(),
            WAFER_ID: sub_test_item_detail.WAFER_ID.clone().unwrap(),
            WAFER_NO: sub_test_item_detail.WAFER_NO.clone().unwrap(),
            WAFER_SIZE: file_detail.WAFER_SIZE,
            WAFER_MARGIN: file_detail.WAFER_MARGIN,
            DIE_HEIGHT: file_detail.DIE_HEIGHT,
            DIE_WIDTH: file_detail.DIE_WIDTH,
            WF_UNITS: file_detail.WF_UNITS,
            WF_FLAT: file_detail.WF_FLAT.clone(),
            CENTER_X: file_detail.CENTER_X,
            CENTER_Y: file_detail.CENTER_Y,
            CENTER_OFFSET_X: file_detail.CENTER_OFFSET_X,
            CENTER_OFFSET_Y: file_detail.CENTER_OFFSET_Y,
            CENTER_RETICLE_X: file_detail.CENTER_RETICLE_X,
            CENTER_RETICLE_Y: file_detail.CENTER_RETICLE_Y.map(|v| v as u8),
            CENTER_RETICLE_OFFSET_X: file_detail.CENTER_RETICLE_OFFSET_X,
            CENTER_RETICLE_OFFSET_Y: file_detail.CENTER_RETICLE_OFFSET_Y,
            POS_X: file_detail.POS_X.clone(),
            POS_Y: file_detail.POS_Y.clone(),
            DIE_CNT: file_detail.DIE_CNT,
            RETICLE_T_X: sub_test_item_detail.RETICLE_T_X,
            RETICLE_T_Y: sub_test_item_detail.RETICLE_T_Y,
            RETICLE_X: sub_test_item_detail.RETICLE_X,
            RETICLE_Y: sub_test_item_detail.RETICLE_Y,
            RETICLE_ROW: file_detail.RETICLE_ROW,
            RETICLE_COLUMN: file_detail.RETICLE_COLUMN,
            RETICLE_ROW_CENTER_OFFSET: file_detail.RETICLE_ROW_CENTER_OFFSET,
            RETICLE_COLUMN_CENTER_OFFSET: file_detail.RETICLE_COLUMN_CENTER_OFFSET,
            ORIGINAL_WAFER_SIZE: file_detail.ORIGINAL_WAFER_SIZE,
            ORIGINAL_WAFER_MARGIN: file_detail.ORIGINAL_WAFER_MARGIN,
            ORIGINAL_WF_UNITS: file_detail.ORIGINAL_WF_UNITS,
            ORIGINAL_WF_FLAT: file_detail.ORIGINAL_WF_FLAT.clone(),
            ORIGINAL_POS_X: file_detail.ORIGINAL_POS_X.clone(),
            ORIGINAL_POS_Y: file_detail.ORIGINAL_POS_Y.clone(),
            ORIGINAL_DIE_WIDTH: file_detail.ORIGINAL_DIE_WIDTH,
            ORIGINAL_DIE_HEIGHT: file_detail.ORIGINAL_DIE_HEIGHT,
            ORIGINAL_RETICLE_ROW: file_detail.ORIGINAL_RETICLE_ROW,
            ORIGINAL_RETICLE_COLUMN: file_detail.ORIGINAL_RETICLE_COLUMN,
            ORIGINAL_RETICLE_ROW_CENTER_OFFSET: file_detail.ORIGINAL_RETICLE_ROW_CENTER_OFFSET,
            ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET: file_detail.ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET,
            ORIGINAL_CENTER_X: file_detail.ORIGINAL_CENTER_X,
            ORIGINAL_CENTER_Y: file_detail.ORIGINAL_CENTER_Y,
            ORIGINAL_CENTER_RETICLE_X: file_detail.ORIGINAL_CENTER_RETICLE_X,
            ORIGINAL_CENTER_RETICLE_Y: file_detail.ORIGINAL_CENTER_RETICLE_Y.map(|v| v as u8),
            ORIGINAL_CENTER_OFFSET_X: file_detail.ORIGINAL_CENTER_OFFSET_X,
            ORIGINAL_CENTER_OFFSET_Y: file_detail.ORIGINAL_CENTER_OFFSET_Y,
            ORIGINAL_CENTER_RETICLE_OFFSET_X: file_detail.ORIGINAL_CENTER_RETICLE_OFFSET_X,
            ORIGINAL_CENTER_RETICLE_OFFSET_Y: file_detail.ORIGINAL_CENTER_RETICLE_OFFSET_Y,
            SITE_ID: sub_test_item_detail.SITE_ID.clone().unwrap(),
            PART_CNT: file_detail.PART_CNT,
            RTST_CNT: file_detail.RTST_CNT,
            ABRT_CNT: file_detail.ABRT_CNT,
            GOOD_CNT: file_detail.GOOD_CNT,
            FUNC_CNT: file_detail.FUNC_CNT,
            FABWF_ID: file_detail.FABWF_ID.clone(),
            FRAME_ID: file_detail.FRAME_ID.clone(),
            MASK_ID: file_detail.MASK_ID.clone(),
            WAFER_USR_DESC: file_detail.WAFER_USR_DESC.clone(),
            WAFER_EXC_DESC: file_detail.WAFER_EXC_DESC.clone(),
            SETUP_T: file_detail.SETUP_T,
            STAT_NUM: file_detail.STAT_NUM,
            MODE_COD: file_detail.MODE_COD.clone(),
            PROT_COD: file_detail.PROT_COD.clone(),
            BURN_TIM: file_detail.BURN_TIM,
            CMOD_COD: file_detail.CMOD_COD.clone(),
            EXEC_TYP: file_detail.EXEC_TYP.clone(),
            EXEC_VER: file_detail.EXEC_VER.clone(),
            USER_TXT: file_detail.USER_TXT.clone(),
            AUX_FILE: file_detail.AUX_FILE.clone(),
            PKG_TYP: file_detail.PKG_TYP.clone(),
            FAMLY_ID: file_detail.FAMLY_ID.clone(),
            DATE_COD: file_detail.DATE_COD.clone(),
            FACIL_ID: file_detail.FACIL_ID.clone(),
            FLOOR_ID: file_detail.FLOOR_ID.clone(),
            PROC_ID: file_detail.PROC_ID.clone(),
            OPER_FRQ: file_detail.OPER_FRQ.clone(),
            FLOW_ID: file_detail.FLOW_ID.clone(),
            FLOW_ID_IGNORE_TP: file_detail.FLOW_ID_IGNORE_TP.clone(),
            SETUP_ID: file_detail.SETUP_ID.clone(),
            DSGN_REV: file_detail.DSGN_REV.clone(),
            ENG_ID: file_detail.ENG_ID.clone(),
            ROM_COD: file_detail.ROM_COD.clone(),
            SERL_NUM: file_detail.SERL_NUM.clone(),
            SUPR_NAM: file_detail.SUPR_NAM.clone(),
            DISP_COD: file_detail.DISP_COD.clone(),
            LOT_USR_DESC: file_detail.LOT_USR_DESC.clone(),
            LOT_EXC_DESC: file_detail.LOT_EXC_DESC.clone(),
            DIB_TYP: file_detail.DIB_TYP.clone(),
            DIB_ID: file_detail.DIB_ID.clone(),
            CABL_TYP: file_detail.CABL_TYP.clone(),
            CABL_ID: file_detail.CABL_ID.clone(),
            CONT_TYP: file_detail.CONT_TYP.clone(),
            CONT_ID: file_detail.CONT_ID.clone(),
            LASR_TYP: file_detail.LASR_TYP.clone(),
            LASR_ID: file_detail.LASR_ID.clone(),
            EXTR_TYP: file_detail.EXTR_TYP.clone(),
            EXTR_ID: file_detail.EXTR_ID.clone(),
            RETEST_BIN_NUM: file_detail.RETEST_BIN_NUM.clone(),
            VECT_NAM: sub_test_item_detail.VECT_NAM.clone().unwrap(),
            TIME_SET: sub_test_item_detail.TIME_SET.clone().unwrap(),
            NUM_FAIL: sub_test_item_detail.NUM_FAIL.map(|i| i as u32),
            FAIL_PIN: sub_test_item_detail.FAIL_PIN.clone().unwrap(),
            CYCL_CNT: sub_test_item_detail.CYCL_CNT.map(|i| i as u32),
            REPT_CNT: sub_test_item_detail.REPT_CNT.map(|i| i as u32),
            LONG_ATTRIBUTE_SET: sub_test_item_detail
                .LONG_ATTRIBUTE_SET
                .as_ref()
                .map(|map| {
                    map.iter()
                        .map(|(k, v)| (k.clone(), *v))
                        .collect::<Vec<(String, i64)>>()
                })
                .unwrap_or_default(),
            STRING_ATTRIBUTE_SET: sub_test_item_detail
                .STRING_ATTRIBUTE_SET
                .as_ref()
                .map(|map| {
                    map.iter()
                        .map(|(k, v)| (k.clone(), v.clone()))
                        .collect::<Vec<(String, String)>>()
                })
                .unwrap_or_default(),
            FLOAT_ATTRIBUTE_SET: sub_test_item_detail
                .FLOAT_ATTRIBUTE_SET
                .as_ref()
                .map(|map| {
                    map.iter()
                        .map(|(k, v)| (k.clone(), v.into_decimal38_18()))
                        .collect::<Vec<(String, Decimal38_18)>>()
                })
                .unwrap_or_default(),
            UID: sub_test_item_detail.UID.clone().unwrap(),
            TEXT_DAT: DwdCommonUtil::string_value(sub_test_item_detail.TEXT_DAT.clone()),
            CREATE_HOUR_KEY: sub_test_item_detail.CREATE_HOUR_KEY.clone().unwrap(),
            CREATE_DAY_KEY: sub_test_item_detail.CREATE_DAY_KEY.clone().unwrap(),
            CREATE_TIME: sub_test_item_detail.CREATE_TIME.into_utc(),
            CREATE_USER: file_detail.CREATE_USER.clone(),
            LOT_BUCKET: file_detail.LOT_BUCKET,
            IS_DELETE: file_detail.IS_DELETE,
            PROCESS: DwdCommonUtil::string_value(file_detail.PROCESS.clone()),
            UPLOAD_TIME: DwdCommonUtil::cal_upload_time(file_detail.UPLOAD_TIME, sub_test_item_detail.CREATE_TIME.into_utc()),
            DATA_VERSION: DwdCommonUtil::cal_data_version(file_detail.DATA_VERSION),
            EFUSE_EXTRA: sub_test_item_detail
                .EFUSE_EXTRA
                .as_ref()
                .map(|map| {
                    map.iter()
                        .map(|(k, v)| (k.clone(), v.clone()))
                        .collect::<Vec<(String, String)>>()
                })
                .unwrap_or_default(),
            CHIP_ID: sub_test_item_detail.CHIP_ID.clone().unwrap(),
        }
    }

    /// Optimized constructor that minimizes string clones by using references where possible
    /// and only cloning when absolutely necessary for owned data
    pub fn new_optimized(sub_test_item_detail: &SubTestItemDetail, file_detail: &FileDetail) -> Self {
        // For now, this is the same as the original new method
        // The real optimization would require changing the struct to use Cow<str> or Arc<str>
        // But that would be a breaking change requiring updates throughout the codebase
        Self::new(sub_test_item_detail, file_detail)
    }
}
